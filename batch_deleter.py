#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为云OBS大规模文件批量删除工具
支持上亿文件的分批处理和断点续传
"""

import os
import sys
import json
import logging
import time
import threading
from typing import List, Dict, Optional, Generator
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import hashlib
from obs import ObsClient
import argparse
from datetime import datetime


class LargeScaleOBSDeleter:
    """大规模OBS文件删除器"""
    
    def __init__(self, config_path: str):
        """
        初始化删除器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self.load_config(config_path)
        self.obs_client = None
        self.logger = None
        self.processed_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.not_found_count = 0
        self.checkpoint_file = "delete_checkpoint.json"
        self.lock = threading.Lock()
        
        self.setup_logging()
        self.setup_obs_client()
        self.setup_temp_directory()
        
    def load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 处理环境变量替换
        obs_settings = config['obs_settings']
        for key, value in obs_settings.items():
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                obs_settings[key] = os.getenv(env_var, value)
                
        return config
        
    def setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, self.config['delete_settings']['log_level'])
        log_format = '%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f'obs_batch_delete_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_obs_client(self):
        """设置OBS客户端"""
        obs_config = self.config['obs_settings']
        perf_config = self.config['performance']
        
        self.obs_client = ObsClient(
            access_key_id=obs_config['access_key'],
            secret_access_key=obs_config['secret_key'],
            server=obs_config['endpoint'],
            timeout=perf_config['connection_timeout']
        )
        
    def setup_temp_directory(self):
        """设置临时目录"""
        temp_dir = Path(self.config['file_processing']['temp_dir'])
        temp_dir.mkdir(exist_ok=True)
        
    def split_file_list(self, file_list_path: str) -> Generator[str, None, None]:
        """
        将大文件列表分割成小块
        
        Args:
            file_list_path: 文件列表路径
            
        Yields:
            分块文件路径
        """
        chunk_size = self.config['file_processing']['chunk_size']
        temp_dir = Path(self.config['file_processing']['temp_dir'])
        
        self.logger.info(f"开始分割文件列表: {file_list_path}")
        
        with open(file_list_path, 'r', encoding='utf-8') as f:
            chunk_num = 0
            current_chunk = []
            
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                    
                current_chunk.append(line)
                
                if len(current_chunk) >= chunk_size:
                    chunk_file = temp_dir / f"chunk_{chunk_num:06d}.txt"
                    self.save_chunk(current_chunk, chunk_file)
                    yield str(chunk_file)
                    
                    current_chunk = []
                    chunk_num += 1
                    
                    if line_num % 100000 == 0:
                        self.logger.info(f"已处理 {line_num} 行，生成 {chunk_num} 个分块")
            
            # 处理最后一个分块
            if current_chunk:
                chunk_file = temp_dir / f"chunk_{chunk_num:06d}.txt"
                self.save_chunk(current_chunk, chunk_file)
                yield str(chunk_file)
                
        self.logger.info(f"文件分割完成，共生成 {chunk_num + 1} 个分块")
        
    def save_chunk(self, file_list: List[str], chunk_file: Path):
        """保存分块文件"""
        with open(chunk_file, 'w', encoding='utf-8') as f:
            for file_path in file_list:
                f.write(f"{file_path}\n")
                
    def load_checkpoint(self) -> dict:
        """加载检查点"""
        if not os.path.exists(self.checkpoint_file):
            return {
                'processed_chunks': [],
                'last_chunk': None,
                'total_processed': 0,
                'total_success': 0,
                'total_failed': 0,
                'total_not_found': 0
            }
            
        with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    def save_checkpoint(self, chunk_file: str, results: dict):
        """保存检查点"""
        checkpoint = self.load_checkpoint()
        checkpoint['processed_chunks'].append(chunk_file)
        checkpoint['last_chunk'] = chunk_file
        checkpoint['total_processed'] += results.get('total', 0)
        checkpoint['total_success'] += results.get('success', 0)
        checkpoint['total_failed'] += results.get('failed', 0)
        checkpoint['total_not_found'] += results.get('not_found', 0)
        checkpoint['last_update'] = datetime.now().isoformat()
        
        with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint, f, indent=2, ensure_ascii=False)
            
    def batch_delete_chunk(self, chunk_file: str) -> dict:
        """删除单个分块的文件"""
        self.logger.info(f"开始处理分块: {chunk_file}")
        
        # 读取分块文件
        with open(chunk_file, 'r', encoding='utf-8') as f:
            file_list = [line.strip() for line in f if line.strip()]
            
        if not file_list:
            return {'success': 0, 'failed': 0, 'not_found': 0, 'total': 0}
            
        # 分批删除
        batch_size = self.config['delete_settings']['batch_size']
        results = {'success': 0, 'failed': 0, 'not_found': 0, 'total': len(file_list)}
        
        for i in range(0, len(file_list), batch_size):
            batch = file_list[i:i + batch_size]
            batch_result = self.delete_batch(batch)
            
            # 累计结果
            for key in ['success', 'failed', 'not_found']:
                results[key] += batch_result.get(key, 0)
                
            # 更新进度
            with self.lock:
                self.processed_count += len(batch)
                self.success_count += batch_result.get('success', 0)
                self.failed_count += batch_result.get('failed', 0)
                self.not_found_count += batch_result.get('not_found', 0)
                
                if self.processed_count % self.config['delete_settings']['progress_report_interval'] == 0:
                    self.logger.info(f"进度报告 - 已处理: {self.processed_count}, "
                                   f"成功: {self.success_count}, "
                                   f"失败: {self.failed_count}, "
                                   f"不存在: {self.not_found_count}")
                    
        self.logger.info(f"分块处理完成: {chunk_file}, 结果: {results}")
        return results
        
    def delete_batch(self, file_list: List[str]) -> dict:
        """删除一批文件"""
        if self.config['delete_settings']['enable_dry_run']:
            # 试运行模式
            return self.dry_run_batch(file_list)
            
        retry_attempts = self.config['delete_settings']['retry_attempts']
        retry_delay = self.config['delete_settings']['retry_delay_seconds']
        
        for attempt in range(retry_attempts):
            try:
                delete_objects = [{'key': key} for key in file_list]
                resp = self.obs_client.deleteObjects(
                    self.config['obs_settings']['bucket_name'], 
                    delete_objects
                )
                
                if resp.status < 300:
                    results = {'success': 0, 'failed': 0, 'not_found': 0}
                    
                    # 处理成功删除的文件
                    if hasattr(resp.body, 'deleted') and resp.body.deleted:
                        results['success'] = len(resp.body.deleted)
                        
                    # 处理删除失败的文件
                    if hasattr(resp.body, 'error') and resp.body.error:
                        for error in resp.body.error:
                            if error.code == 'NoSuchKey':
                                results['not_found'] += 1
                            else:
                                results['failed'] += 1
                                
                    return results
                else:
                    self.logger.warning(f"批量删除失败 (尝试 {attempt + 1}/{retry_attempts}): "
                                      f"{resp.status} - {resp.errorMessage}")
                    
            except Exception as e:
                self.logger.error(f"批量删除异常 (尝试 {attempt + 1}/{retry_attempts}): {e}")
                
            if attempt < retry_attempts - 1:
                time.sleep(retry_delay)
                
        # 所有重试都失败
        return {'success': 0, 'failed': len(file_list), 'not_found': 0}
        
    def dry_run_batch(self, file_list: List[str]) -> dict:
        """试运行批处理"""
        results = {'success': 0, 'failed': 0, 'not_found': 0}
        
        for file_path in file_list:
            try:
                resp = self.obs_client.getObjectMetadata(
                    self.config['obs_settings']['bucket_name'], 
                    file_path
                )
                if resp.status < 300:
                    results['success'] += 1
                else:
                    results['not_found'] += 1
            except Exception:
                results['not_found'] += 1
                
        return results
        
    def process_large_file_list(self, file_list_path: str):
        """处理大规模文件列表"""
        self.logger.info("开始大规模文件删除任务")
        
        # 加载检查点
        checkpoint = self.load_checkpoint()
        processed_chunks = set(checkpoint.get('processed_chunks', []))
        
        # 恢复计数器
        self.processed_count = checkpoint.get('total_processed', 0)
        self.success_count = checkpoint.get('total_success', 0)
        self.failed_count = checkpoint.get('total_failed', 0)
        self.not_found_count = checkpoint.get('total_not_found', 0)
        
        if self.config['file_processing']['resume_from_checkpoint'] and processed_chunks:
            self.logger.info(f"从检查点恢复，已处理 {len(processed_chunks)} 个分块")
            
        # 分割文件列表并并发处理
        max_workers = self.config['delete_settings']['max_concurrent_batches']
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {}
            
            for chunk_file in self.split_file_list(file_list_path):
                # 跳过已处理的分块
                if chunk_file in processed_chunks:
                    self.logger.info(f"跳过已处理的分块: {chunk_file}")
                    continue
                    
                future = executor.submit(self.batch_delete_chunk, chunk_file)
                futures[future] = chunk_file
                
            # 处理完成的任务
            for future in as_completed(futures):
                chunk_file = futures[future]
                try:
                    results = future.result()
                    self.save_checkpoint(chunk_file, results)
                    
                    # 备份已处理的分块
                    if self.config['file_processing']['backup_processed_lists']:
                        backup_dir = Path("processed_chunks")
                        backup_dir.mkdir(exist_ok=True)
                        backup_file = backup_dir / Path(chunk_file).name
                        os.rename(chunk_file, backup_file)
                    else:
                        os.remove(chunk_file)
                        
                except Exception as e:
                    self.logger.error(f"处理分块失败: {chunk_file}, 错误: {e}")
                    
        # 输出最终统计
        self.logger.info("=== 删除任务完成 ===")
        self.logger.info(f"总处理文件数: {self.processed_count}")
        self.logger.info(f"成功删除: {self.success_count}")
        self.logger.info(f"删除失败: {self.failed_count}")
        self.logger.info(f"文件不存在: {self.not_found_count}")
        
    def close(self):
        """关闭客户端"""
        if self.obs_client:
            self.obs_client.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='华为云OBS大规模文件批量删除工具')
    parser.add_argument('--config', required=True, help='配置文件路径')
    parser.add_argument('--file-list', required=True, help='要删除的文件清单路径')
    parser.add_argument('--reset-checkpoint', action='store_true', help='重置检查点，从头开始')
    
    args = parser.parse_args()
    
    # 重置检查点
    if args.reset_checkpoint:
        checkpoint_file = "delete_checkpoint.json"
        if os.path.exists(checkpoint_file):
            os.remove(checkpoint_file)
            print("检查点已重置")
    
    # 创建删除器
    deleter = LargeScaleOBSDeleter(args.config)
    
    try:
        deleter.process_large_file_list(args.file_list)
    except KeyboardInterrupt:
        print("\n用户中断，保存当前进度...")
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)
    finally:
        deleter.close()


if __name__ == '__main__':
    main()
