#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件设置助手
帮助用户快速配置华为云OBS访问密钥
"""

import json
import os
import sys
from pathlib import Path


def create_config_from_template():
    """从模板创建配置文件"""
    template_file = "obs_config_template.json"
    config_file = "obs_config.json"
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return False
        
    if os.path.exists(config_file):
        response = input(f"⚠️  配置文件 {config_file} 已存在，是否覆盖？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return False
            
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 移除注释字段
        if '_comment' in config:
            del config['_comment']
            
        # 移除参考信息
        if '_endpoints_reference' in config['obs_settings']:
            del config['obs_settings']['_endpoints_reference']
            
        # 移除所有注释字段
        for section in config.values():
            if isinstance(section, dict):
                keys_to_remove = [k for k in section.keys() if k.startswith('_')]
                for key in keys_to_remove:
                    del section[key]
                    
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        print(f"✅ 配置文件已创建: {config_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False


def interactive_setup():
    """交互式配置设置"""
    print("🔧 华为云OBS配置向导")
    print("=" * 40)
    
    # 获取用户输入
    print("\n请输入华为云OBS配置信息：")
    
    access_key = input("访问密钥ID (Access Key): ").strip()
    if not access_key:
        print("❌ 访问密钥ID不能为空")
        return False
        
    secret_key = input("访问密钥 (Secret Key): ").strip()
    if not secret_key:
        print("❌ 访问密钥不能为空")
        return False
        
    bucket_name = input("存储桶名称: ").strip()
    if not bucket_name:
        print("❌ 存储桶名称不能为空")
        return False
        
    # 选择区域
    print("\n请选择区域：")
    regions = {
        "1": ("cn-north-1", "https://obs.cn-north-1.myhuaweicloud.com", "北京一"),
        "2": ("cn-north-4", "https://obs.cn-north-4.myhuaweicloud.com", "北京四"),
        "3": ("cn-east-3", "https://obs.cn-east-3.myhuaweicloud.com", "上海一"),
        "4": ("cn-east-2", "https://obs.cn-east-2.myhuaweicloud.com", "上海二"),
        "5": ("cn-south-1", "https://obs.cn-south-1.myhuaweicloud.com", "广州"),
        "6": ("ap-southeast-1", "https://obs.ap-southeast-1.myhuaweicloud.com", "香港")
    }
    
    for key, (region, endpoint, name) in regions.items():
        print(f"{key}. {name} ({region})")
        
    region_choice = input("\n请选择区域 (默认: 5-广州): ").strip() or "5"
    
    if region_choice not in regions:
        print("❌ 无效的区域选择")
        return False
        
    region, endpoint, region_name = regions[region_choice]
    
    # 其他设置
    print(f"\n✅ 已选择区域: {region_name}")
    
    dry_run = input("是否启用试运行模式？(Y/n): ").strip().lower()
    enable_dry_run = dry_run != 'n'
    
    # 创建配置
    config = {
        "obs_settings": {
            "access_key": access_key,
            "secret_key": secret_key,
            "endpoint": endpoint,
            "bucket_name": bucket_name,
            "region": region
        },
        "delete_settings": {
            "batch_size": 1000,
            "max_concurrent_batches": 5,
            "retry_attempts": 3,
            "retry_delay_seconds": 2,
            "enable_dry_run": enable_dry_run,
            "log_level": "INFO",
            "progress_report_interval": 10000
        },
        "file_processing": {
            "chunk_size": 100000,
            "temp_dir": "./temp_chunks",
            "backup_processed_lists": True,
            "resume_from_checkpoint": True,
            "checkpoint_interval": 50000
        },
        "performance": {
            "connection_timeout": 30,
            "read_timeout": 60,
            "max_retry_time": 300,
            "enable_multipart_threshold": 104857600
        }
    }
    
    # 保存配置
    config_file = "obs_config.json"
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        print(f"\n✅ 配置文件已保存: {config_file}")
        print(f"🔧 区域: {region_name}")
        print(f"🪣 存储桶: {bucket_name}")
        print(f"🧪 试运行模式: {'启用' if enable_dry_run else '禁用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存配置文件失败: {e}")
        return False


def validate_config():
    """验证配置文件"""
    config_file = "obs_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
        
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 检查必需字段
        obs_settings = config.get('obs_settings', {})
        required_fields = ['access_key', 'secret_key', 'endpoint', 'bucket_name', 'region']
        
        missing_fields = []
        placeholder_fields = []
        
        for field in required_fields:
            if field not in obs_settings:
                missing_fields.append(field)
            elif obs_settings[field] in ['your_access_key_here', 'your_secret_key_here', 'your-bucket-name']:
                placeholder_fields.append(field)
                
        if missing_fields:
            print(f"❌ 配置文件缺少必需字段: {', '.join(missing_fields)}")
            return False
            
        if placeholder_fields:
            print(f"⚠️  配置文件包含占位符，请设置实际值: {', '.join(placeholder_fields)}")
            return False
            
        print("✅ 配置文件验证通过")
        print(f"🔧 区域: {obs_settings['region']}")
        print(f"🪣 存储桶: {obs_settings['bucket_name']}")
        print(f"🧪 试运行模式: {'启用' if config.get('delete_settings', {}).get('enable_dry_run', True) else '禁用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证配置文件失败: {e}")
        return False


def show_config():
    """显示当前配置"""
    config_file = "obs_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return
        
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        print("📋 当前配置信息")
        print("=" * 30)
        
        obs_settings = config.get('obs_settings', {})
        delete_settings = config.get('delete_settings', {})
        
        print(f"🔑 访问密钥ID: {obs_settings.get('access_key', 'N/A')[:8]}...")
        print(f"🔐 访问密钥: {'*' * 8}...")
        print(f"🌐 端点: {obs_settings.get('endpoint', 'N/A')}")
        print(f"🪣 存储桶: {obs_settings.get('bucket_name', 'N/A')}")
        print(f"🗺️  区域: {obs_settings.get('region', 'N/A')}")
        print(f"🧪 试运行模式: {'启用' if delete_settings.get('enable_dry_run', True) else '禁用'}")
        print(f"📦 批处理大小: {delete_settings.get('batch_size', 'N/A')}")
        print(f"🔄 并发批次: {delete_settings.get('max_concurrent_batches', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")


def main():
    """主函数"""
    print("🔧 华为云OBS配置助手")
    print("=" * 40)
    
    while True:
        print("\n请选择操作：")
        print("1. 交互式配置设置")
        print("2. 从模板创建配置文件")
        print("3. 验证配置文件")
        print("4. 显示当前配置")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            interactive_setup()
        elif choice == '2':
            create_config_from_template()
        elif choice == '3':
            validate_config()
        elif choice == '4':
            show_config()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")


if __name__ == '__main__':
    main()
