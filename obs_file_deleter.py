#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为云OBS文件批量删除工具
根据清单文件删除指定的OBS文件
"""

import os
import sys
import json
import logging
from typing import List, Dict, Optional
from obs import ObsClient
import argparse
from datetime import datetime


class HuaweiOBSDeleter:
    """华为云OBS文件删除器"""
    
    def __init__(self, access_key: str, secret_key: str, endpoint: str, bucket_name: str):
        """
        初始化OBS客户端
        
        Args:
            access_key: 华为云访问密钥ID
            secret_key: 华为云访问密钥
            endpoint: OBS服务端点（广州：obs.cn-south-1.myhuaweicloud.com）
            bucket_name: 存储桶名称
        """
        self.access_key = access_key
        self.secret_key = secret_key
        self.endpoint = endpoint
        self.bucket_name = bucket_name
        
        # 初始化OBS客户端
        self.obs_client = ObsClient(
            access_key_id=access_key,
            secret_access_key=secret_key,
            server=endpoint
        )
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志配置"""
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler(f'obs_delete_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_file_list(self, file_path: str) -> List[str]:
        """
        从文件中加载要删除的文件清单
        
        Args:
            file_path: 清单文件路径，支持txt、json格式
            
        Returns:
            文件路径列表
        """
        file_list = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    # JSON格式
                    data = json.load(f)
                    if isinstance(data, list):
                        file_list = data
                    elif isinstance(data, dict) and 'files' in data:
                        file_list = data['files']
                    else:
                        raise ValueError("JSON格式不正确，应为文件路径数组或包含'files'字段的对象")
                else:
                    # 文本格式，每行一个文件路径
                    file_list = [line.strip() for line in f.readlines() if line.strip()]
                    
            self.logger.info(f"成功加载 {len(file_list)} 个文件路径")
            return file_list
            
        except Exception as e:
            self.logger.error(f"加载文件清单失败: {e}")
            raise
            
    def check_file_exists(self, object_key: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            object_key: 对象键（文件路径）
            
        Returns:
            文件是否存在
        """
        try:
            resp = self.obs_client.getObjectMetadata(self.bucket_name, object_key)
            return resp.status < 300
        except Exception:
            return False
            
    def delete_single_file(self, object_key: str) -> bool:
        """
        删除单个文件
        
        Args:
            object_key: 对象键（文件路径）
            
        Returns:
            删除是否成功
        """
        try:
            resp = self.obs_client.deleteObject(self.bucket_name, object_key)
            if resp.status < 300:
                self.logger.info(f"成功删除文件: {object_key}")
                return True
            else:
                self.logger.error(f"删除文件失败: {object_key}, 状态码: {resp.status}, 错误: {resp.errorMessage}")
                return False
        except Exception as e:
            self.logger.error(f"删除文件异常: {object_key}, 错误: {e}")
            return False
            
    def batch_delete_files(self, object_keys: List[str], batch_size: int = 1000) -> Dict[str, int]:
        """
        批量删除文件
        
        Args:
            object_keys: 对象键列表
            batch_size: 批量删除的大小（华为云OBS最大支持1000个）
            
        Returns:
            删除结果统计
        """
        results = {
            'success': 0,
            'failed': 0,
            'not_found': 0,
            'total': len(object_keys)
        }
        
        # 分批处理
        for i in range(0, len(object_keys), batch_size):
            batch = object_keys[i:i + batch_size]
            self.logger.info(f"处理批次 {i//batch_size + 1}, 文件数量: {len(batch)}")
            
            try:
                # 构建删除对象列表
                delete_objects = [{'key': key} for key in batch]
                
                # 执行批量删除
                resp = self.obs_client.deleteObjects(self.bucket_name, delete_objects)
                
                if resp.status < 300:
                    # 处理删除结果
                    if hasattr(resp.body, 'deleted') and resp.body.deleted:
                        for deleted in resp.body.deleted:
                            results['success'] += 1
                            self.logger.info(f"成功删除: {deleted.key}")
                            
                    if hasattr(resp.body, 'error') and resp.body.error:
                        for error in resp.body.error:
                            if error.code == 'NoSuchKey':
                                results['not_found'] += 1
                                self.logger.warning(f"文件不存在: {error.key}")
                            else:
                                results['failed'] += 1
                                self.logger.error(f"删除失败: {error.key}, 错误: {error.code} - {error.message}")
                else:
                    self.logger.error(f"批量删除请求失败: {resp.status} - {resp.errorMessage}")
                    results['failed'] += len(batch)
                    
            except Exception as e:
                self.logger.error(f"批量删除异常: {e}")
                results['failed'] += len(batch)
                
        return results
        
    def delete_files_from_list(self, file_list_path: str, dry_run: bool = False, 
                              use_batch: bool = True) -> Dict[str, int]:
        """
        根据清单删除文件
        
        Args:
            file_list_path: 文件清单路径
            dry_run: 是否为试运行（只检查不删除）
            use_batch: 是否使用批量删除
            
        Returns:
            删除结果统计
        """
        # 加载文件清单
        file_list = self.load_file_list(file_list_path)
        
        if dry_run:
            self.logger.info("=== 试运行模式，只检查文件存在性 ===")
            exists_count = 0
            not_exists_count = 0
            
            for object_key in file_list:
                if self.check_file_exists(object_key):
                    exists_count += 1
                    self.logger.info(f"文件存在: {object_key}")
                else:
                    not_exists_count += 1
                    self.logger.warning(f"文件不存在: {object_key}")
                    
            return {
                'exists': exists_count,
                'not_exists': not_exists_count,
                'total': len(file_list)
            }
        
        # 执行删除
        if use_batch:
            self.logger.info("使用批量删除模式")
            return self.batch_delete_files(file_list)
        else:
            self.logger.info("使用单个删除模式")
            results = {'success': 0, 'failed': 0, 'not_found': 0, 'total': len(file_list)}
            
            for object_key in file_list:
                if not self.check_file_exists(object_key):
                    results['not_found'] += 1
                    self.logger.warning(f"文件不存在: {object_key}")
                    continue
                    
                if self.delete_single_file(object_key):
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    
            return results
            
    def close(self):
        """关闭OBS客户端"""
        if self.obs_client:
            self.obs_client.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='华为云OBS文件批量删除工具')
    parser.add_argument('--access-key', required=True, help='华为云访问密钥ID')
    parser.add_argument('--secret-key', required=True, help='华为云访问密钥')
    parser.add_argument('--bucket', required=True, help='存储桶名称')
    parser.add_argument('--file-list', required=True, help='要删除的文件清单路径')
    parser.add_argument('--endpoint', default='https://obs.cn-south-1.myhuaweicloud.com', 
                       help='OBS服务端点（默认：广州）')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式，只检查不删除')
    parser.add_argument('--single-mode', action='store_true', help='使用单个删除模式而非批量删除')
    
    args = parser.parse_args()
    
    # 创建删除器
    deleter = HuaweiOBSDeleter(
        access_key=args.access_key,
        secret_key=args.secret_key,
        endpoint=args.endpoint,
        bucket_name=args.bucket
    )
    
    try:
        # 执行删除
        results = deleter.delete_files_from_list(
            file_list_path=args.file_list,
            dry_run=args.dry_run,
            use_batch=not args.single_mode
        )
        
        # 输出结果
        print("\n=== 删除结果统计 ===")
        for key, value in results.items():
            print(f"{key}: {value}")
            
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)
    finally:
        deleter.close()


if __name__ == '__main__':
    main()
