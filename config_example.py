#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为云OBS配置示例和便捷使用脚本
"""

import os
from obs_file_deleter import HuaweiOBSDeleter

# 华为云OBS配置
OBS_CONFIG = {
    # 华为云访问密钥（建议通过环境变量设置）
    'access_key': os.getenv('HUAWEI_ACCESS_KEY', 'your_access_key_here'),
    'secret_key': os.getenv('HUAWEI_SECRET_KEY', 'your_secret_key_here'),
    
    # 存储桶配置
    'bucket_name': 'your-bucket-name',
    
    # 广州区域端点
    'endpoint': 'https://obs.cn-south-1.myhuaweicloud.com',
    
    # 其他区域端点参考：
    # 北京一: 'https://obs.cn-north-1.myhuaweicloud.com'
    # 北京四: 'https://obs.cn-north-4.myhuaweicloud.com'
    # 上海一: 'https://obs.cn-east-3.myhuaweicloud.com'
    # 上海二: 'https://obs.cn-east-2.myhuaweicloud.com'
    # 广州: 'https://obs.cn-south-1.myhuaweicloud.com'
    # 香港: 'https://obs.ap-southeast-1.myhuaweicloud.com'
}


def delete_files_simple(file_list_path: str, dry_run: bool = True):
    """
    简化的文件删除函数
    
    Args:
        file_list_path: 文件清单路径
        dry_run: 是否为试运行模式
    """
    deleter = HuaweiOBSDeleter(
        access_key=OBS_CONFIG['access_key'],
        secret_key=OBS_CONFIG['secret_key'],
        endpoint=OBS_CONFIG['endpoint'],
        bucket_name=OBS_CONFIG['bucket_name']
    )
    
    try:
        results = deleter.delete_files_from_list(file_list_path, dry_run=dry_run)
        
        print("\n=== 删除结果统计 ===")
        for key, value in results.items():
            print(f"{key}: {value}")
            
        return results
    finally:
        deleter.close()


if __name__ == '__main__':
    # 使用示例
    
    # 1. 试运行模式（推荐先运行）
    print("=== 试运行模式 ===")
    delete_files_simple('file_list.txt', dry_run=True)
    
    # 2. 确认无误后，实际删除（取消注释下面的代码）
    # print("\n=== 实际删除 ===")
    # delete_files_simple('file_list.txt', dry_run=False)
