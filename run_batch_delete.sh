#!/bin/bash
# 华为云OBS大规模文件删除脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置文件路径
CONFIG_FILE="obs_config.json"
FILE_LIST=""
DRY_RUN=false
RESET_CHECKPOINT=false

# 显示帮助信息
show_help() {
    echo "华为云OBS大规模文件删除工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE        配置文件路径 (默认: obs_config.json)"
    echo "  -f, --file-list FILE     文件列表路径 (必需)"
    echo "  -d, --dry-run           试运行模式"
    echo "  -r, --reset-checkpoint  重置检查点"
    echo "  -h, --help              显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -f large_file_list.txt -d                    # 试运行"
    echo "  $0 -f large_file_list.txt                       # 实际删除"
    echo "  $0 -f large_file_list.txt -r                    # 重置检查点后删除"
    echo "  $0 -c custom_config.json -f file_list.txt       # 使用自定义配置"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -f|--file-list)
            FILE_LIST="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -r|--reset-checkpoint)
            RESET_CHECKPOINT=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}错误: 未知参数 $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [[ -z "$FILE_LIST" ]]; then
    echo -e "${RED}错误: 必须指定文件列表路径 (-f)${NC}"
    show_help
    exit 1
fi

# 检查文件是否存在
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${RED}错误: 配置文件不存在: $CONFIG_FILE${NC}"
    exit 1
fi

if [[ ! -f "$FILE_LIST" ]]; then
    echo -e "${RED}错误: 文件列表不存在: $FILE_LIST${NC}"
    exit 1
fi

# 显示配置信息
echo -e "${BLUE}=== 华为云OBS大规模文件删除工具 ===${NC}"
echo -e "${BLUE}配置文件:${NC} $CONFIG_FILE"
echo -e "${BLUE}文件列表:${NC} $FILE_LIST"
echo -e "${BLUE}试运行模式:${NC} $DRY_RUN"
echo -e "${BLUE}重置检查点:${NC} $RESET_CHECKPOINT"
echo ""

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到 python3${NC}"
    exit 1
fi

# 检查依赖包
echo -e "${YELLOW}检查Python依赖包...${NC}"
if ! python3 -c "import obs" &> /dev/null; then
    echo -e "${YELLOW}安装华为云OBS SDK...${NC}"
    pip3 install esdk-obs-python
fi

# 更新配置文件中的试运行设置
if [[ "$DRY_RUN" == true ]]; then
    echo -e "${YELLOW}启用试运行模式...${NC}"
    python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
config['delete_settings']['enable_dry_run'] = True
with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, indent=2)
"
else
    echo -e "${YELLOW}启用实际删除模式...${NC}"
    python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
config['delete_settings']['enable_dry_run'] = False
with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, indent=2)
"
fi

# 统计文件数量
echo -e "${YELLOW}统计文件数量...${NC}"
TOTAL_FILES=$(grep -v '^#' "$FILE_LIST" | grep -v '^$' | wc -l)
echo -e "${BLUE}待处理文件总数:${NC} $TOTAL_FILES"

# 确认操作
if [[ "$DRY_RUN" == false ]]; then
    echo ""
    echo -e "${RED}警告: 这将删除 $TOTAL_FILES 个文件，此操作不可逆！${NC}"
    echo -e "${YELLOW}请确认您已经备份了重要数据。${NC}"
    echo ""
    read -p "确认继续删除操作？(输入 'YES' 确认): " confirm
    
    if [[ "$confirm" != "YES" ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        exit 0
    fi
fi

# 构建命令参数
PYTHON_ARGS="--config $CONFIG_FILE --file-list $FILE_LIST"
if [[ "$RESET_CHECKPOINT" == true ]]; then
    PYTHON_ARGS="$PYTHON_ARGS --reset-checkpoint"
fi

# 创建必要的目录
mkdir -p logs
mkdir -p temp_chunks
mkdir -p processed_chunks

# 执行删除任务
echo ""
echo -e "${GREEN}开始执行删除任务...${NC}"
echo -e "${BLUE}日志文件将保存在 logs/ 目录${NC}"
echo -e "${BLUE}可以使用 Ctrl+C 安全中断任务${NC}"
echo ""

# 运行Python脚本
python3 batch_deleter.py $PYTHON_ARGS

# 检查执行结果
if [[ $? -eq 0 ]]; then
    echo ""
    echo -e "${GREEN}=== 任务执行完成 ===${NC}"
    
    # 显示检查点信息
    if [[ -f "delete_checkpoint.json" ]]; then
        echo -e "${BLUE}最终统计信息:${NC}"
        python3 -c "
import json
try:
    with open('delete_checkpoint.json', 'r') as f:
        checkpoint = json.load(f)
    print(f'总处理文件数: {checkpoint.get(\"total_processed\", 0)}')
    print(f'成功删除: {checkpoint.get(\"total_success\", 0)}')
    print(f'删除失败: {checkpoint.get(\"total_failed\", 0)}')
    print(f'文件不存在: {checkpoint.get(\"total_not_found\", 0)}')
    print(f'已处理分块数: {len(checkpoint.get(\"processed_chunks\", []))}')
except:
    print('无法读取检查点文件')
"
    fi
else
    echo ""
    echo -e "${RED}=== 任务执行失败 ===${NC}"
    echo -e "${YELLOW}请检查日志文件获取详细错误信息${NC}"
    exit 1
fi
