# 华为云OBS文件批量删除工具

这是一个用于批量删除华为云OBS存储桶中文件的Python工具，支持根据文件清单进行批量删除操作。

## 功能特性

- ✅ 支持批量删除和单个删除两种模式
- ✅ 支持试运行模式，删除前可以先检查文件
- ✅ 支持TXT和JSON两种清单格式
- ✅ 完整的日志记录和错误处理
- ✅ 针对广州区域优化配置
- ✅ 支持中文文件路径
- ✅ 详细的删除结果统计

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装华为云OBS SDK：

```bash
pip install esdk-obs-python
```

## 配置说明

### 1. 获取华为云访问密钥

1. 登录华为云控制台
2. 进入"我的凭证" > "访问密钥"
3. 创建访问密钥，获取Access Key ID和Secret Access Key

### 2. 配置存储桶信息

- 存储桶名称：您的OBS存储桶名称
- 区域：广州区域 (cn-south-1)
- 端点：https://obs.cn-south-1.myhuaweicloud.com

## 使用方法

### 方法一：命令行使用

```bash
# 试运行模式（推荐先执行）
python obs_file_deleter.py \
  --access-key "your_access_key" \
  --secret-key "your_secret_key" \
  --bucket "your-bucket-name" \
  --file-list "file_list.txt" \
  --dry-run

# 实际删除
python obs_file_deleter.py \
  --access-key "your_access_key" \
  --secret-key "your_secret_key" \
  --bucket "your-bucket-name" \
  --file-list "file_list.txt"

# 使用单个删除模式
python obs_file_deleter.py \
  --access-key "your_access_key" \
  --secret-key "your_secret_key" \
  --bucket "your-bucket-name" \
  --file-list "file_list.txt" \
  --single-mode
```

### 方法二：使用配置文件

1. 修改 `config_example.py` 中的配置信息
2. 运行配置脚本：

```bash
python config_example.py
```

### 方法三：环境变量方式

```bash
# 设置环境变量
export HUAWEI_ACCESS_KEY="your_access_key"
export HUAWEI_SECRET_KEY="your_secret_key"

# 运行脚本
python obs_file_deleter.py \
  --bucket "your-bucket-name" \
  --file-list "file_list.txt" \
  --dry-run
```

## 文件清单格式

### TXT格式 (file_list.txt)

```
# 注释行以 # 开头
images/2023/01/photo1.jpg
documents/reports/report_2023.pdf
videos/backup/old_video.mp4
文档/报告/年度总结.docx
```

### JSON格式 (file_list.json)

```json
{
  "description": "删除清单描述",
  "files": [
    "images/2023/01/photo1.jpg",
    "documents/reports/report_2023.pdf",
    "videos/backup/old_video.mp4",
    "文档/报告/年度总结.docx"
  ]
}
```

## 命令行参数说明

| 参数 | 必需 | 说明 |
|------|------|------|
| `--access-key` | 是 | 华为云访问密钥ID |
| `--secret-key` | 是 | 华为云访问密钥 |
| `--bucket` | 是 | 存储桶名称 |
| `--file-list` | 是 | 文件清单路径 |
| `--endpoint` | 否 | OBS端点（默认广州） |
| `--dry-run` | 否 | 试运行模式 |
| `--single-mode` | 否 | 单个删除模式 |

## 安全建议

1. **先使用试运行模式**：确保要删除的文件列表正确
2. **备份重要数据**：删除操作不可逆，请确保有备份
3. **使用环境变量**：避免在代码中硬编码密钥
4. **权限最小化**：使用专门的删除权限账号

## 日志文件

程序运行时会生成日志文件：`obs_delete_YYYYMMDD_HHMMSS.log`

日志包含：
- 删除操作的详细记录
- 错误信息和异常处理
- 删除结果统计

## 错误处理

- 文件不存在：记录警告，继续处理其他文件
- 网络错误：记录错误，可重试
- 权限错误：检查访问密钥和存储桶权限
- 配置错误：检查端点和存储桶名称

## 注意事项

1. 华为云OBS批量删除单次最多支持1000个文件
2. 删除操作不可逆，请谨慎操作
3. 建议在非生产环境先测试
4. 大量文件删除时注意API调用频率限制

## 示例文件

- `file_list_example.txt` - TXT格式清单示例
- `file_list_example.json` - JSON格式清单示例
- `config_example.py` - 配置文件示例

## 故障排除

### 常见问题

1. **认证失败**：检查访问密钥是否正确
2. **存储桶不存在**：检查存储桶名称和区域
3. **网络连接失败**：检查网络和端点配置
4. **权限不足**：确保账号有删除权限

### 联系支持

如遇到问题，请检查日志文件中的详细错误信息。
