# 华为云OBS大规模文件批量删除工具

这是一个专为处理上亿文件设计的华为云OBS批量删除工具，支持分块处理、断点续传和并发删除。

## 🚀 核心特性

### 大规模处理能力
- ✅ **支持上亿文件**：自动分块处理，无文件数量限制
- ✅ **断点续传**：支持中断后从检查点恢复
- ✅ **并发处理**：多线程并发删除，提高效率
- ✅ **内存优化**：流式处理，低内存占用

### 配置与管理
- ✅ **配置文件分离**：独立的JSON配置文件
- ✅ **灵活的文件列表**：支持多种文件列表格式
- ✅ **试运行模式**：删除前安全检查
- ✅ **详细日志**：完整的操作记录和统计

### 安全与可靠性
- ✅ **错误重试**：自动重试失败的操作
- ✅ **进度监控**：实时进度报告
- ✅ **安全中断**：支持Ctrl+C安全停止
- ✅ **备份机制**：可选的已处理文件备份

## 📁 文件结构

```
huaweiobsdelete/
├── obs_config.json          # 主配置文件
├── obs_config_template.json # 配置文件模板
├── setup_config.py          # 配置助手工具
├── smart_obs_deleter.py     # 智能删除工具（支持保护文件和断点续传）
├── batch_deleter.py         # 大规模批量删除工具
├── file_list_generator.py   # 文件列表生成器
├── checkpoint_manager.py    # 检查点管理工具
├── url_decoder.py           # URL解码工具
├── extract_obs_paths.py     # 路径提取工具
├── run_smart_delete.sh      # 智能删除执行脚本
├── run_batch_delete.sh      # 批量删除执行脚本
├── obs_file_deleter.py      # 基础删除工具
├── config_example.py        # 配置示例
├── test_smart_delete.py     # 功能测试工具
├── requirements.txt         # 依赖包
├── README.md               # 说明文档
├── SMART_DELETE_GUIDE.md   # 智能删除使用指南
└── QUICK_START.md          # 快速开始指南
```

## 🛠️ 安装与配置

### 1. 安装依赖

```bash
pip install esdk-obs-python
```

### 2. 配置文件设置

#### 方法一：使用配置助手（推荐）

```bash
python setup_config.py
```

按照提示输入您的华为云访问密钥和存储桶信息。

#### 方法二：手动编辑配置文件

复制模板并编辑：

```bash
cp obs_config_template.json obs_config.json
```

然后编辑 `obs_config.json`，将占位符替换为实际值：

```json
{
  "obs_settings": {
    "access_key": "your_access_key_here",
    "secret_key": "your_secret_key_here",
    "endpoint": "https://obs.cn-south-1.myhuaweicloud.com",
    "bucket_name": "your-bucket-name",
    "region": "cn-south-1"
  },
  "delete_settings": {
    "batch_size": 1000,
    "max_concurrent_batches": 5,
    "retry_attempts": 3,
    "enable_dry_run": true
  }
}
```

#### 验证配置

```bash
python setup_config.py  # 选择验证配置选项
```

## 🚀 使用方法

### 方法一：智能删除（推荐，支持保护文件和断点续传）

```bash
# 试运行模式（推荐先执行）
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt -d

# 实际删除（自动跳过保护文件）
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt

# 重置检查点后删除
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt -r

# 不从检查点恢复，从头开始
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt -n
```

### 方法二：大规模批量删除

```bash
# 试运行模式（推荐先执行）
./run_batch_delete.sh -f large_file_list.txt -d

# 实际删除
./run_batch_delete.sh -f large_file_list.txt

# 重置检查点后删除
./run_batch_delete.sh -f large_file_list.txt -r

# 使用自定义配置
./run_batch_delete.sh -c custom_config.json -f file_list.txt
```

### 方法三：直接使用Python脚本

```bash
# 智能删除（支持保护文件和断点续传）
python smart_obs_deleter.py \
  --config obs_config.json \
  --file-list delete_list.txt \
  --protected-files fail_decoded.txt

# 大规模批量删除（推荐用于上亿文件）
python batch_deleter.py \
  --config obs_config.json \
  --file-list large_file_list.txt

# 基础批量删除（适用于较少文件）
python obs_file_deleter.py \
  --access-key "your_access_key" \
  --secret-key "your_secret_key" \
  --bucket "your-bucket-name" \
  --file-list "file_list.txt" \
  --dry-run
```

### 方法四：生成文件列表

```bash
# 从OBS生成文件列表
python file_list_generator.py from-obs \
  --config obs_config.json \
  --output large_file_list.txt \
  --prefix "old_data/"

# 按日期范围生成列表
python file_list_generator.py from-obs \
  --config obs_config.json \
  --output files_2023.txt \
  --start-date "2023-01-01" \
  --end-date "2023-12-31"

# 分割大文件列表
python file_list_generator.py split \
  --config obs_config.json \
  --input huge_file_list.txt \
  --chunk-size 1000000
```

### 方法五：检查点管理

```bash
# 查看当前进度
python checkpoint_manager.py status

# 估算剩余时间
python checkpoint_manager.py estimate --total-files 1000000

# 重置检查点
python checkpoint_manager.py reset

# 设置检查点到指定行
python checkpoint_manager.py set 500000

# 备份检查点
python checkpoint_manager.py backup
```

## 文件清单格式

### TXT格式 (file_list.txt)

```
# 注释行以 # 开头
images/2023/01/photo1.jpg
documents/reports/report_2023.pdf
videos/backup/old_video.mp4
文档/报告/年度总结.docx
```

### JSON格式 (file_list.json)

```json
{
  "description": "删除清单描述",
  "files": [
    "images/2023/01/photo1.jpg",
    "documents/reports/report_2023.pdf",
    "videos/backup/old_video.mp4",
    "文档/报告/年度总结.docx"
  ]
}
```

## 命令行参数说明

| 参数 | 必需 | 说明 |
|------|------|------|
| `--access-key` | 是 | 华为云访问密钥ID |
| `--secret-key` | 是 | 华为云访问密钥 |
| `--bucket` | 是 | 存储桶名称 |
| `--file-list` | 是 | 文件清单路径 |
| `--endpoint` | 否 | OBS端点（默认广州） |
| `--dry-run` | 否 | 试运行模式 |
| `--single-mode` | 否 | 单个删除模式 |

## 安全建议

1. **先使用试运行模式**：确保要删除的文件列表正确
2. **备份重要数据**：删除操作不可逆，请确保有备份
3. **使用环境变量**：避免在代码中硬编码密钥
4. **权限最小化**：使用专门的删除权限账号

## 日志文件

程序运行时会生成日志文件：`obs_delete_YYYYMMDD_HHMMSS.log`

日志包含：
- 删除操作的详细记录
- 错误信息和异常处理
- 删除结果统计

## 错误处理

- 文件不存在：记录警告，继续处理其他文件
- 网络错误：记录错误，可重试
- 权限错误：检查访问密钥和存储桶权限
- 配置错误：检查端点和存储桶名称

## 注意事项

1. 华为云OBS批量删除单次最多支持1000个文件
2. 删除操作不可逆，请谨慎操作
3. 建议在非生产环境先测试
4. 大量文件删除时注意API调用频率限制

## 示例文件

- `file_list_example.txt` - TXT格式清单示例
- `file_list_example.json` - JSON格式清单示例
- `config_example.py` - 配置文件示例

## 故障排除

### 常见问题

1. **认证失败**：检查访问密钥是否正确
2. **存储桶不存在**：检查存储桶名称和区域
3. **网络连接失败**：检查网络和端点配置
4. **权限不足**：确保账号有删除权限

### 联系支持

如遇到问题，请检查日志文件中的详细错误信息。
