#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能华为云OBS文件删除工具
- 根据清单列表删除文件
- 自动跳过保护文件列表中的文件
- 支持断点续传功能
"""

import os
import sys
import json
import logging
import time
import threading
from typing import List, Dict, Set, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import hashlib
from obs import ObsClient
import argparse
from datetime import datetime
import urllib.parse


class SmartOBSDeleter:
    """智能OBS文件删除器"""
    
    def __init__(self, config_path: str, protected_files_path: str = None):
        """
        初始化删除器
        
        Args:
            config_path: 配置文件路径
            protected_files_path: 保护文件列表路径（如fail_decoded.txt）
        """
        self.config = self.load_config(config_path)
        self.obs_client = None
        self.logger = None
        self.protected_files = set()
        self.processed_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        self.protected_count = 0
        self.checkpoint_file = "smart_delete_checkpoint.json"
        self.lock = threading.Lock()
        
        # 加载保护文件列表
        if protected_files_path:
            self.load_protected_files(protected_files_path)
            
        self.setup_logging()
        self.setup_obs_client()
        
    def load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        return config
        
    def load_protected_files(self, protected_files_path: str):
        """
        加载保护文件列表
        
        Args:
            protected_files_path: 保护文件列表路径
        """
        try:
            with open(protected_files_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 如果是完整URL，提取路径部分
                if line.startswith('http'):
                    try:
                        parsed = urllib.parse.urlparse(line)
                        path = parsed.path.lstrip('/')
                        self.protected_files.add(path)
                    except:
                        continue
                else:
                    # 直接是文件路径
                    self.protected_files.add(line)
                    
            self.logger.info(f"已加载 {len(self.protected_files)} 个保护文件")
            
        except Exception as e:
            self.logger.error(f"加载保护文件列表失败: {e}")
            
    def setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, self.config['delete_settings']['log_level'])
        log_format = '%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f'smart_obs_delete_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_obs_client(self):
        """设置OBS客户端"""
        obs_config = self.config['obs_settings']
        perf_config = self.config['performance']
        
        self.obs_client = ObsClient(
            access_key_id=obs_config['access_key'],
            secret_access_key=obs_config['secret_key'],
            server=obs_config['endpoint'],
            timeout=perf_config['connection_timeout']
        )
        
    def is_protected_file(self, file_path: str) -> bool:
        """
        检查文件是否在保护列表中
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为保护文件
        """
        return file_path in self.protected_files
        
    def load_checkpoint(self) -> dict:
        """加载检查点"""
        if not os.path.exists(self.checkpoint_file):
            return {
                'last_processed_line': 0,
                'total_processed': 0,
                'total_success': 0,
                'total_failed': 0,
                'total_skipped': 0,
                'total_protected': 0,
                'start_time': datetime.now().isoformat()
            }
            
        with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    def save_checkpoint(self, line_number: int):
        """保存检查点"""
        checkpoint = {
            'last_processed_line': line_number,
            'total_processed': self.processed_count,
            'total_success': self.success_count,
            'total_failed': self.failed_count,
            'total_skipped': self.skipped_count,
            'total_protected': self.protected_count,
            'last_update': datetime.now().isoformat()
        }
        
        with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint, f, indent=2, ensure_ascii=False)
            
    def delete_single_file(self, file_path: str) -> dict:
        """
        删除单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            删除结果
        """
        # 检查是否为保护文件
        if self.is_protected_file(file_path):
            self.logger.info(f"跳过保护文件: {file_path}")
            return {'status': 'protected', 'message': '保护文件，跳过删除'}
            
        if self.config['delete_settings']['enable_dry_run']:
            # 试运行模式
            try:
                resp = self.obs_client.getObjectMetadata(
                    self.config['obs_settings']['bucket_name'], 
                    file_path
                )
                if resp.status < 300:
                    return {'status': 'success', 'message': '试运行：文件存在'}
                else:
                    return {'status': 'not_found', 'message': '试运行：文件不存在'}
            except Exception as e:
                return {'status': 'not_found', 'message': f'试运行：检查失败 - {e}'}
        
        # 实际删除
        retry_attempts = self.config['delete_settings']['retry_attempts']
        retry_delay = self.config['delete_settings']['retry_delay_seconds']
        
        for attempt in range(retry_attempts):
            try:
                resp = self.obs_client.deleteObject(
                    self.config['obs_settings']['bucket_name'], 
                    file_path
                )
                
                if resp.status < 300:
                    return {'status': 'success', 'message': '删除成功'}
                elif resp.status == 404:
                    return {'status': 'not_found', 'message': '文件不存在'}
                else:
                    self.logger.warning(f"删除失败 (尝试 {attempt + 1}/{retry_attempts}): "
                                      f"{file_path}, 状态: {resp.status}")
                    
            except Exception as e:
                self.logger.error(f"删除异常 (尝试 {attempt + 1}/{retry_attempts}): "
                                f"{file_path}, 错误: {e}")
                
            if attempt < retry_attempts - 1:
                time.sleep(retry_delay)
                
        return {'status': 'failed', 'message': '删除失败，已重试所有次数'}
        
    def process_file_list(self, file_list_path: str, resume: bool = True):
        """
        处理文件列表
        
        Args:
            file_list_path: 文件列表路径
            resume: 是否从检查点恢复
        """
        self.logger.info("开始智能文件删除任务")
        
        # 加载检查点
        checkpoint = self.load_checkpoint()
        start_line = 0
        
        if resume and checkpoint.get('last_processed_line', 0) > 0:
            start_line = checkpoint['last_processed_line']
            self.processed_count = checkpoint.get('total_processed', 0)
            self.success_count = checkpoint.get('total_success', 0)
            self.failed_count = checkpoint.get('total_failed', 0)
            self.skipped_count = checkpoint.get('total_skipped', 0)
            self.protected_count = checkpoint.get('total_protected', 0)
            
            self.logger.info(f"从检查点恢复，从第 {start_line + 1} 行开始处理")
            
        # 读取文件列表
        try:
            with open(file_list_path, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
        except Exception as e:
            self.logger.error(f"读取文件列表失败: {e}")
            return
            
        total_lines = len(all_lines)
        self.logger.info(f"文件列表总行数: {total_lines}")
        
        # 从指定行开始处理
        lines_to_process = all_lines[start_line:]
        
        progress_interval = self.config['delete_settings']['progress_report_interval']
        checkpoint_interval = self.config['file_processing']['checkpoint_interval']
        
        for i, line in enumerate(lines_to_process):
            current_line = start_line + i
            line = line.strip()
            
            if not line or line.startswith('#'):
                continue
                
            # 如果是完整URL，提取路径部分
            if line.startswith('http'):
                try:
                    parsed = urllib.parse.urlparse(line)
                    file_path = parsed.path.lstrip('/')
                except:
                    self.logger.warning(f"无法解析URL: {line}")
                    continue
            else:
                file_path = line
                
            # 删除文件
            result = self.delete_single_file(file_path)
            
            # 更新计数器
            with self.lock:
                self.processed_count += 1
                
                if result['status'] == 'success':
                    self.success_count += 1
                    self.logger.info(f"成功删除: {file_path}")
                elif result['status'] == 'failed':
                    self.failed_count += 1
                    self.logger.error(f"删除失败: {file_path} - {result['message']}")
                elif result['status'] == 'not_found':
                    self.skipped_count += 1
                    self.logger.warning(f"文件不存在: {file_path}")
                elif result['status'] == 'protected':
                    self.protected_count += 1
                    
                # 进度报告
                if self.processed_count % progress_interval == 0:
                    progress = (current_line + 1) / total_lines * 100
                    self.logger.info(f"进度: {progress:.1f}% ({current_line + 1}/{total_lines}) - "
                                   f"成功: {self.success_count}, "
                                   f"失败: {self.failed_count}, "
                                   f"不存在: {self.skipped_count}, "
                                   f"保护: {self.protected_count}")
                    
                # 保存检查点
                if (current_line + 1) % checkpoint_interval == 0:
                    self.save_checkpoint(current_line + 1)
                    self.logger.info(f"检查点已保存: 第 {current_line + 1} 行")
                    
        # 保存最终检查点
        self.save_checkpoint(total_lines)
        
        # 输出最终统计
        self.logger.info("=== 删除任务完成 ===")
        self.logger.info(f"总处理文件数: {self.processed_count}")
        self.logger.info(f"成功删除: {self.success_count}")
        self.logger.info(f"删除失败: {self.failed_count}")
        self.logger.info(f"文件不存在: {self.skipped_count}")
        self.logger.info(f"保护文件跳过: {self.protected_count}")
        
    def close(self):
        """关闭客户端"""
        if self.obs_client:
            self.obs_client.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智能华为云OBS文件删除工具')
    parser.add_argument('--config', required=True, help='配置文件路径')
    parser.add_argument('--file-list', required=True, help='要删除的文件清单路径')
    parser.add_argument('--protected-files', help='保护文件列表路径（如fail_decoded.txt）')
    parser.add_argument('--no-resume', action='store_true', help='不从检查点恢复，从头开始')
    parser.add_argument('--reset-checkpoint', action='store_true', help='重置检查点')
    
    args = parser.parse_args()
    
    # 重置检查点
    if args.reset_checkpoint:
        checkpoint_file = "smart_delete_checkpoint.json"
        if os.path.exists(checkpoint_file):
            os.remove(checkpoint_file)
            print("检查点已重置")
    
    # 创建删除器
    deleter = SmartOBSDeleter(args.config, args.protected_files)
    
    try:
        deleter.process_file_list(args.file_list, resume=not args.no_resume)
    except KeyboardInterrupt:
        print("\n用户中断，保存当前进度...")
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)
    finally:
        deleter.close()


if __name__ == '__main__':
    main()
