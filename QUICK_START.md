# 快速开始指南

## 🚀 5分钟快速上手

### 步骤1：安装依赖
```bash
pip install esdk-obs-python
```

### 步骤2：配置访问密钥
```bash
# 方法1：使用环境变量（推荐）
export HUAWEI_ACCESS_KEY="your_access_key"
export HUAWEI_SECRET_KEY="your_secret_key"

# 方法2：复制环境变量模板
cp .env.example .env
# 编辑 .env 文件，填入实际密钥
source .env
```

### 步骤3：修改配置文件
编辑 `obs_config.json`，修改存储桶名称：
```json
{
  "obs_settings": {
    "bucket_name": "your-actual-bucket-name"
  }
}
```

### 步骤4：准备文件列表
创建要删除的文件列表 `my_files.txt`：
```
path/to/file1.jpg
path/to/file2.pdf
old_data/backup.zip
```

### 步骤5：试运行（重要！）
```bash
./run_batch_delete.sh -f my_files.txt -d
```

### 步骤6：确认无误后实际删除
```bash
./run_batch_delete.sh -f my_files.txt
```

## 📋 处理大规模文件列表

### 如果您有上千万或上亿文件：

#### 1. 从OBS生成文件列表
```bash
# 生成指定前缀的所有文件
python file_list_generator.py from-obs \
  --config obs_config.json \
  --output large_file_list.txt \
  --prefix "old_data/"

# 按日期范围生成
python file_list_generator.py from-obs \
  --config obs_config.json \
  --output files_2023.txt \
  --start-date "2023-01-01" \
  --end-date "2023-12-31"
```

#### 2. 分割大文件列表（可选）
```bash
python file_list_generator.py split \
  --config obs_config.json \
  --input large_file_list.txt \
  --chunk-size 1000000
```

#### 3. 执行大规模删除
```bash
# 试运行
./run_batch_delete.sh -f large_file_list.txt -d

# 实际删除
./run_batch_delete.sh -f large_file_list.txt
```

## ⚠️ 重要提醒

1. **务必先试运行**：使用 `-d` 参数进行试运行
2. **备份重要数据**：删除操作不可逆
3. **检查文件列表**：确保列表中的文件路径正确
4. **监控进度**：大规模删除时注意查看日志
5. **断点续传**：如果中断，可以直接重新运行继续

## 🔧 常用配置调整

### 提高删除速度
在 `obs_config.json` 中调整：
```json
{
  "delete_settings": {
    "batch_size": 1000,
    "max_concurrent_batches": 10
  }
}
```

### 降低API调用频率
```json
{
  "delete_settings": {
    "batch_size": 500,
    "max_concurrent_batches": 2
  }
}
```

## 📊 监控删除进度

### 查看实时日志
```bash
tail -f logs/obs_batch_delete_*.log
```

### 查看检查点状态
```bash
cat delete_checkpoint.json
```

## 🆘 遇到问题？

1. **认证失败**：检查访问密钥是否正确
2. **网络超时**：调整配置文件中的超时设置
3. **权限不足**：确保账号有删除权限
4. **文件不存在**：正常情况，会在日志中记录

查看详细错误信息：
```bash
grep "ERROR" logs/obs_batch_delete_*.log
```
