#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从解码后的URL中提取OBS文件路径
将完整的HTTP URL转换为OBS对象键（文件路径）
"""

import urllib.parse
import sys
import argparse
from pathlib import Path


def extract_obs_path(url):
    """
    从完整URL中提取OBS对象键
    
    Args:
        url: 完整的HTTP URL
        
    Returns:
        OBS对象键（文件路径）
    """
    try:
        # 解析URL
        parsed = urllib.parse.urlparse(url)
        
        # 获取路径部分，去掉开头的斜杠
        path = parsed.path.lstrip('/')
        
        return path
    except Exception as e:
        print(f"提取路径失败: {url}, 错误: {e}")
        return None


def process_decoded_file(input_file, output_file=None):
    """
    处理解码后的URL文件，提取OBS路径
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        obs_paths = []
        skipped_count = 0
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            # 提取OBS路径
            obs_path = extract_obs_path(line)
            if obs_path:
                obs_paths.append(obs_path)
            else:
                skipped_count += 1
                
            # 显示进度
            if line_num % 100 == 0:
                print(f"已处理 {line_num} 行...")
                
        # 输出结果
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                for path in obs_paths:
                    f.write(f"{path}\n")
            print(f"提取完成，共 {len(obs_paths)} 个文件路径已保存到: {output_file}")
        else:
            print("=== 提取的OBS路径 ===")
            for i, path in enumerate(obs_paths[:20], 1):  # 只显示前20行
                print(f"{i:3d}: {path}")
            if len(obs_paths) > 20:
                print(f"... 还有 {len(obs_paths) - 20} 个路径")
                
        if skipped_count > 0:
            print(f"跳过了 {skipped_count} 个无效URL")
            
        return obs_paths
        
    except Exception as e:
        print(f"处理文件失败: {e}")
        return None


def analyze_paths(obs_paths):
    """
    分析OBS路径的统计信息
    
    Args:
        obs_paths: OBS路径列表
    """
    if not obs_paths:
        return
        
    print("\n=== 路径分析 ===")
    
    # 统计目录分布
    dir_count = {}
    file_types = {}
    
    for path in obs_paths:
        # 统计目录
        parts = path.split('/')
        if len(parts) > 0:
            top_dir = parts[0]
            dir_count[top_dir] = dir_count.get(top_dir, 0) + 1
            
        # 统计文件类型
        if '.' in path:
            ext = Path(path).suffix.lower()
            file_types[ext] = file_types.get(ext, 0) + 1
            
    print(f"总文件数: {len(obs_paths)}")
    
    print("\n顶级目录分布:")
    for dir_name, count in sorted(dir_count.items(), key=lambda x: x[1], reverse=True):
        print(f"  {dir_name}: {count} 个文件")
        
    print("\n文件类型分布:")
    for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
        if ext:
            print(f"  {ext}: {count} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='从解码URL中提取OBS文件路径')
    parser.add_argument('input_file', help='输入文件路径（解码后的URL文件）')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('--analyze', action='store_true', help='分析路径统计信息')
    
    args = parser.parse_args()
    
    # 提取OBS路径
    obs_paths = process_decoded_file(args.input_file, args.output)
    
    # 分析路径
    if args.analyze and obs_paths:
        analyze_paths(obs_paths)


if __name__ == '__main__':
    # 如果没有命令行参数，直接处理fail_decoded.txt
    if len(sys.argv) == 1:
        print("正在从 fail_decoded.txt 提取OBS文件路径...")
        obs_paths = process_decoded_file('fail_decoded.txt', 'obs_file_paths.txt')
        if obs_paths:
            analyze_paths(obs_paths)
    else:
        main()
