#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查点管理工具
用于查看、管理和操作删除任务的检查点
"""

import json
import os
import sys
import argparse
from datetime import datetime
from pathlib import Path


class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, checkpoint_file="smart_delete_checkpoint.json"):
        self.checkpoint_file = checkpoint_file
        
    def load_checkpoint(self):
        """加载检查点"""
        if not os.path.exists(self.checkpoint_file):
            return None
            
        try:
            with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载检查点失败: {e}")
            return None
            
    def show_status(self):
        """显示检查点状态"""
        checkpoint = self.load_checkpoint()
        
        if not checkpoint:
            print("❌ 没有找到检查点文件")
            return
            
        print("📊 检查点状态信息")
        print("=" * 50)
        
        # 基本信息
        last_line = checkpoint.get('last_processed_line', 0)
        total_processed = checkpoint.get('total_processed', 0)
        
        print(f"📍 最后处理行: {last_line:,}")
        print(f"📈 总处理文件数: {total_processed:,}")
        
        # 删除统计
        success = checkpoint.get('total_success', 0)
        failed = checkpoint.get('total_failed', 0)
        skipped = checkpoint.get('total_skipped', 0)
        protected = checkpoint.get('total_protected', 0)
        
        print(f"✅ 成功删除: {success:,}")
        print(f"❌ 删除失败: {failed:,}")
        print(f"⚠️  文件不存在: {skipped:,}")
        print(f"🛡️  保护文件跳过: {protected:,}")
        
        # 成功率
        if total_processed > 0:
            success_rate = (success / total_processed) * 100
            print(f"📊 成功率: {success_rate:.1f}%")
            
        # 时间信息
        start_time = checkpoint.get('start_time')
        last_update = checkpoint.get('last_update')
        
        if start_time:
            print(f"🕐 开始时间: {start_time}")
        if last_update:
            print(f"🕑 最后更新: {last_update}")
            
        # 计算运行时间
        if start_time and last_update:
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                update_dt = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                duration = update_dt - start_dt
                
                hours = duration.total_seconds() / 3600
                print(f"⏱️  运行时长: {hours:.1f} 小时")
                
                if total_processed > 0:
                    rate = total_processed / hours
                    print(f"🚀 处理速度: {rate:.0f} 文件/小时")
                    
            except Exception:
                pass
                
    def estimate_remaining(self, total_files):
        """估算剩余时间"""
        checkpoint = self.load_checkpoint()
        
        if not checkpoint:
            print("❌ 没有找到检查点文件")
            return
            
        last_line = checkpoint.get('last_processed_line', 0)
        remaining_files = total_files - last_line
        
        if remaining_files <= 0:
            print("🎉 所有文件已处理完成！")
            return
            
        print(f"📋 剩余文件估算")
        print("=" * 30)
        print(f"📁 总文件数: {total_files:,}")
        print(f"✅ 已处理: {last_line:,}")
        print(f"⏳ 剩余: {remaining_files:,}")
        
        progress = (last_line / total_files) * 100
        print(f"📊 进度: {progress:.1f}%")
        
        # 估算剩余时间
        start_time = checkpoint.get('start_time')
        last_update = checkpoint.get('last_update')
        
        if start_time and last_update and last_line > 0:
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                update_dt = datetime.fromisoformat(last_update.replace('Z', '+00:00'))
                elapsed = update_dt - start_dt
                
                rate = last_line / elapsed.total_seconds()  # 文件/秒
                remaining_seconds = remaining_files / rate
                remaining_hours = remaining_seconds / 3600
                
                print(f"⏰ 预计剩余时间: {remaining_hours:.1f} 小时")
                
                # 预计完成时间
                completion_time = datetime.now() + timedelta(seconds=remaining_seconds)
                print(f"🏁 预计完成时间: {completion_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
            except Exception as e:
                print(f"⚠️  无法估算剩余时间: {e}")
                
    def reset_checkpoint(self):
        """重置检查点"""
        if os.path.exists(self.checkpoint_file):
            backup_file = f"{self.checkpoint_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(self.checkpoint_file, backup_file)
            print(f"✅ 检查点已重置，备份保存为: {backup_file}")
        else:
            print("ℹ️  没有找到检查点文件")
            
    def set_checkpoint(self, line_number):
        """设置检查点到指定行"""
        checkpoint = self.load_checkpoint()
        
        if not checkpoint:
            print("❌ 没有找到检查点文件，无法修改")
            return
            
        checkpoint['last_processed_line'] = line_number
        checkpoint['last_update'] = datetime.now().isoformat()
        
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint, f, indent=2, ensure_ascii=False)
            print(f"✅ 检查点已设置到第 {line_number:,} 行")
        except Exception as e:
            print(f"❌ 设置检查点失败: {e}")
            
    def backup_checkpoint(self):
        """备份检查点"""
        if not os.path.exists(self.checkpoint_file):
            print("❌ 没有找到检查点文件")
            return
            
        backup_file = f"{self.checkpoint_file}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            import shutil
            shutil.copy2(self.checkpoint_file, backup_file)
            print(f"✅ 检查点已备份为: {backup_file}")
        except Exception as e:
            print(f"❌ 备份失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='检查点管理工具')
    parser.add_argument('--checkpoint-file', default='smart_delete_checkpoint.json', 
                       help='检查点文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='操作命令')
    
    # 显示状态
    status_parser = subparsers.add_parser('status', help='显示检查点状态')
    
    # 估算剩余
    estimate_parser = subparsers.add_parser('estimate', help='估算剩余时间')
    estimate_parser.add_argument('--total-files', type=int, required=True, 
                                help='总文件数')
    
    # 重置检查点
    reset_parser = subparsers.add_parser('reset', help='重置检查点')
    
    # 设置检查点
    set_parser = subparsers.add_parser('set', help='设置检查点到指定行')
    set_parser.add_argument('line_number', type=int, help='行号')
    
    # 备份检查点
    backup_parser = subparsers.add_parser('backup', help='备份检查点')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    manager = CheckpointManager(args.checkpoint_file)
    
    if args.command == 'status':
        manager.show_status()
    elif args.command == 'estimate':
        manager.estimate_remaining(args.total_files)
    elif args.command == 'reset':
        manager.reset_checkpoint()
    elif args.command == 'set':
        manager.set_checkpoint(args.line_number)
    elif args.command == 'backup':
        manager.backup_checkpoint()


if __name__ == '__main__':
    main()
