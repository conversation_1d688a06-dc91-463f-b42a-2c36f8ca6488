#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件列表生成器
用于生成大规模文件删除清单
"""

import os
import json
import argparse
from typing import List, Generator
from pathlib import Path
from obs import ObsClient


class FileListGenerator:
    """文件列表生成器"""
    
    def __init__(self, config_path: str):
        """初始化"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
            
        obs_config = self.config['obs_settings']
        self.obs_client = ObsClient(
            access_key_id=obs_config['access_key'],
            secret_access_key=obs_config['secret_key'],
            server=obs_config['endpoint']
        )
        self.bucket_name = obs_config['bucket_name']
        
    def list_objects_by_prefix(self, prefix: str = "", max_keys: int = 1000) -> Generator[str, None, None]:
        """
        按前缀列出对象
        
        Args:
            prefix: 对象前缀
            max_keys: 每次请求的最大对象数
            
        Yields:
            对象键
        """
        marker = ""
        
        while True:
            resp = self.obs_client.listObjects(
                bucketName=self.bucket_name,
                prefix=prefix,
                marker=marker,
                max_keys=max_keys
            )
            
            if resp.status >= 300:
                raise Exception(f"列出对象失败: {resp.status} - {resp.errorMessage}")
                
            if not hasattr(resp.body, 'contents') or not resp.body.contents:
                break
                
            for obj in resp.body.contents:
                yield obj.key
                marker = obj.key
                
            # 检查是否还有更多对象
            if not resp.body.is_truncated:
                break
                
    def list_objects_by_date_range(self, start_date: str, end_date: str, 
                                  prefix: str = "") -> Generator[str, None, None]:
        """
        按日期范围列出对象
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            prefix: 对象前缀
            
        Yields:
            对象键
        """
        from datetime import datetime
        
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        for obj_key in self.list_objects_by_prefix(prefix):
            try:
                # 获取对象元数据
                resp = self.obs_client.getObjectMetadata(self.bucket_name, obj_key)
                if resp.status < 300:
                    last_modified = resp.body.lastModified
                    if start_dt <= last_modified <= end_dt:
                        yield obj_key
            except Exception:
                continue
                
    def generate_file_list_from_obs(self, output_file: str, prefix: str = "", 
                                   start_date: str = None, end_date: str = None):
        """
        从OBS生成文件列表
        
        Args:
            output_file: 输出文件路径
            prefix: 对象前缀过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤
        """
        print(f"开始生成文件列表: {output_file}")
        
        count = 0
        with open(output_file, 'w', encoding='utf-8') as f:
            if start_date and end_date:
                print(f"按日期范围过滤: {start_date} 到 {end_date}")
                obj_generator = self.list_objects_by_date_range(start_date, end_date, prefix)
            else:
                print(f"按前缀过滤: {prefix}")
                obj_generator = self.list_objects_by_prefix(prefix)
                
            for obj_key in obj_generator:
                f.write(f"{obj_key}\n")
                count += 1
                
                if count % 10000 == 0:
                    print(f"已生成 {count} 个文件路径")
                    
        print(f"文件列表生成完成，共 {count} 个文件: {output_file}")
        
    def generate_file_list_from_patterns(self, output_file: str, patterns: List[str]):
        """
        根据模式生成文件列表
        
        Args:
            output_file: 输出文件路径
            patterns: 文件路径模式列表
        """
        import fnmatch
        
        print(f"开始根据模式生成文件列表: {output_file}")
        
        count = 0
        with open(output_file, 'w', encoding='utf-8') as f:
            for obj_key in self.list_objects_by_prefix():
                for pattern in patterns:
                    if fnmatch.fnmatch(obj_key, pattern):
                        f.write(f"{obj_key}\n")
                        count += 1
                        break
                        
                if count % 10000 == 0:
                    print(f"已匹配 {count} 个文件路径")
                    
        print(f"模式匹配完成，共 {count} 个文件: {output_file}")
        
    def split_large_list(self, input_file: str, chunk_size: int = 1000000):
        """
        分割大文件列表
        
        Args:
            input_file: 输入文件路径
            chunk_size: 每个分块的大小
        """
        input_path = Path(input_file)
        output_dir = input_path.parent / f"{input_path.stem}_chunks"
        output_dir.mkdir(exist_ok=True)
        
        print(f"开始分割文件列表: {input_file}")
        
        chunk_num = 0
        line_count = 0
        current_chunk = []
        
        with open(input_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                    
                current_chunk.append(line)
                line_count += 1
                
                if len(current_chunk) >= chunk_size:
                    chunk_file = output_dir / f"chunk_{chunk_num:06d}.txt"
                    with open(chunk_file, 'w', encoding='utf-8') as cf:
                        for file_path in current_chunk:
                            cf.write(f"{file_path}\n")
                            
                    print(f"生成分块 {chunk_num}: {chunk_file} ({len(current_chunk)} 个文件)")
                    
                    current_chunk = []
                    chunk_num += 1
                    
        # 处理最后一个分块
        if current_chunk:
            chunk_file = output_dir / f"chunk_{chunk_num:06d}.txt"
            with open(chunk_file, 'w', encoding='utf-8') as cf:
                for file_path in current_chunk:
                    cf.write(f"{file_path}\n")
                    
            print(f"生成分块 {chunk_num}: {chunk_file} ({len(current_chunk)} 个文件)")
            
        print(f"分割完成，共生成 {chunk_num + 1} 个分块，总计 {line_count} 个文件")
        
    def close(self):
        """关闭客户端"""
        if self.obs_client:
            self.obs_client.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='文件列表生成器')
    parser.add_argument('--config', required=True, help='配置文件路径')
    parser.add_argument('--output', required=True, help='输出文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='操作命令')
    
    # 从OBS生成列表
    obs_parser = subparsers.add_parser('from-obs', help='从OBS生成文件列表')
    obs_parser.add_argument('--prefix', default='', help='对象前缀过滤')
    obs_parser.add_argument('--start-date', help='开始日期 (YYYY-MM-DD)')
    obs_parser.add_argument('--end-date', help='结束日期 (YYYY-MM-DD)')
    
    # 根据模式生成列表
    pattern_parser = subparsers.add_parser('from-patterns', help='根据模式生成文件列表')
    pattern_parser.add_argument('--patterns', nargs='+', required=True, help='文件路径模式')
    
    # 分割大列表
    split_parser = subparsers.add_parser('split', help='分割大文件列表')
    split_parser.add_argument('--input', required=True, help='输入文件路径')
    split_parser.add_argument('--chunk-size', type=int, default=1000000, help='分块大小')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    generator = FileListGenerator(args.config)
    
    try:
        if args.command == 'from-obs':
            generator.generate_file_list_from_obs(
                args.output, 
                args.prefix, 
                args.start_date, 
                args.end_date
            )
        elif args.command == 'from-patterns':
            generator.generate_file_list_from_patterns(args.output, args.patterns)
        elif args.command == 'split':
            generator.split_large_list(args.input, args.chunk_size)
            
    except Exception as e:
        print(f"执行失败: {e}")
    finally:
        generator.close()


if __name__ == '__main__':
    main()
