#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL解码工具
将fail.txt中的URL编码内容解码为可读格式
"""

import urllib.parse
import sys
import argparse


def decode_url(encoded_url):
    """
    解码URL编码的字符串
    
    Args:
        encoded_url: URL编码的字符串
        
    Returns:
        解码后的字符串
    """
    try:
        # 进行URL解码
        decoded = urllib.parse.unquote(encoded_url)
        return decoded
    except Exception as e:
        print(f"解码失败: {encoded_url}, 错误: {e}")
        return encoded_url


def decode_file(input_file, output_file=None):
    """
    解码文件中的所有URL
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None则输出到控制台
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        decoded_lines = []
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                decoded_lines.append('')
                continue
                
            # 解码URL
            decoded_line = decode_url(line)
            decoded_lines.append(decoded_line)
            
            # 显示进度
            if line_num % 100 == 0:
                print(f"已处理 {line_num} 行...")
                
        # 输出结果
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                for line in decoded_lines:
                    f.write(f"{line}\n")
            print(f"解码完成，结果已保存到: {output_file}")
        else:
            print("=== 解码结果 ===")
            for i, line in enumerate(decoded_lines[:20], 1):  # 只显示前20行
                print(f"{i:3d}: {line}")
            if len(decoded_lines) > 20:
                print(f"... 还有 {len(decoded_lines) - 20} 行")
                
        return decoded_lines
        
    except Exception as e:
        print(f"处理文件失败: {e}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='URL解码工具')
    parser.add_argument('input_file', help='输入文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('--show-sample', action='store_true', help='显示解码示例')
    
    args = parser.parse_args()
    
    if args.show_sample:
        # 显示解码示例
        sample_urls = [
            "http%3A%2F%2Fvodcosqy.zshtys888.com%2F188_dir%2F5paw56aP5pif5bCP5a2QW05la29tb2VraXNzYXRlbl1bVXJ1c2VpWWF0c3VyYTIwMjJdWzIwXVsxMDgwcF1bSlBTQ10ubXA01686896131880%2F1686896326056%2F_ca07dd3b9f724592b89868a9f5440dc1_000239.ts",
            "http%3A%2F%2Fvodcosqy.zshtys888.com%2F42_dir%2F6LaF5Lq6Lm1wNA1755242168021%2F1755242889070%2F_aabec80002104264aec848a870e1ad01_000079.ts"
        ]
        
        print("=== URL解码示例 ===")
        for i, url in enumerate(sample_urls, 1):
            print(f"\n示例 {i}:")
            print(f"原始: {url}")
            print(f"解码: {decode_url(url)}")
            
        return
    
    # 解码文件
    decode_file(args.input_file, args.output)


if __name__ == '__main__':
    # 如果没有命令行参数，直接处理fail.txt
    if len(sys.argv) == 1:
        print("正在解码 fail.txt 文件...")
        decode_file('fail.txt', 'fail_decoded.txt')
    else:
        main()
