#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能删除功能测试脚本
用于验证保护文件和断点续传功能
"""

import os
import json
import tempfile
from pathlib import Path


def create_test_files():
    """创建测试文件"""
    print("🔧 创建测试文件...")
    
    # 创建测试配置文件
    test_config = {
        "obs_settings": {
            "access_key": "${HUAWEI_ACCESS_KEY}",
            "secret_key": "${HUAWEI_SECRET_KEY}",
            "endpoint": "https://obs.cn-south-1.myhuaweicloud.com",
            "bucket_name": "test-bucket",
            "region": "cn-south-1"
        },
        "delete_settings": {
            "batch_size": 1000,
            "max_concurrent_batches": 2,
            "retry_attempts": 3,
            "retry_delay_seconds": 1,
            "enable_dry_run": True,
            "log_level": "INFO",
            "progress_report_interval": 5
        },
        "file_processing": {
            "chunk_size": 100000,
            "temp_dir": "./temp_chunks",
            "backup_processed_lists": True,
            "resume_from_checkpoint": True,
            "checkpoint_interval": 10
        },
        "performance": {
            "connection_timeout": 30,
            "read_timeout": 60,
            "max_retry_time": 300,
            "enable_multipart_threshold": 104857600
        }
    }
    
    with open('test_config.json', 'w', encoding='utf-8') as f:
        json.dump(test_config, f, indent=2, ensure_ascii=False)
    
    # 创建测试删除清单
    delete_list = [
        "188_dir/test_file_001.ts",
        "188_dir/test_file_002.ts", 
        "42_dir/protected_file_001.ts",  # 这个在保护列表中
        "188_dir/test_file_003.ts",
        "42_dir/test_file_004.ts",
        "188_dir/protected_file_002.ts",  # 这个在保护列表中
        "188_dir/test_file_005.ts",
        "42_dir/test_file_006.ts",
        "188_dir/test_file_007.ts",
        "42_dir/test_file_008.ts"
    ]
    
    with open('test_delete_list.txt', 'w', encoding='utf-8') as f:
        for file_path in delete_list:
            f.write(f"{file_path}\n")
    
    # 创建保护文件列表
    protected_files = [
        "http://vodcosqy.zshtys888.com/42_dir/protected_file_001.ts",
        "188_dir/protected_file_002.ts",
        "188_dir/another_protected_file.ts"
    ]
    
    with open('test_protected_files.txt', 'w', encoding='utf-8') as f:
        for file_path in protected_files:
            f.write(f"{file_path}\n")
    
    print("✅ 测试文件创建完成")
    print(f"   - test_config.json: 测试配置文件")
    print(f"   - test_delete_list.txt: 删除清单 ({len(delete_list)} 个文件)")
    print(f"   - test_protected_files.txt: 保护文件列表 ({len(protected_files)} 个文件)")


def show_test_scenario():
    """显示测试场景说明"""
    print("\n📋 测试场景说明")
    print("=" * 50)
    print("1. 删除清单包含10个文件")
    print("2. 保护文件列表包含3个文件")
    print("3. 其中2个文件既在删除清单中，又在保护列表中")
    print("4. 预期结果：保护文件会被跳过，其他文件会被处理")
    print("\n🔍 文件分析：")
    
    # 读取文件进行分析
    with open('test_delete_list.txt', 'r') as f:
        delete_files = [line.strip() for line in f if line.strip()]
    
    with open('test_protected_files.txt', 'r') as f:
        protected_files = [line.strip() for line in f if line.strip()]
    
    # 提取保护文件的路径部分
    protected_paths = set()
    for pf in protected_files:
        if pf.startswith('http'):
            # 提取URL路径部分
            from urllib.parse import urlparse
            path = urlparse(pf).path.lstrip('/')
            protected_paths.add(path)
        else:
            protected_paths.add(pf)
    
    print(f"📁 删除清单文件: {len(delete_files)} 个")
    for df in delete_files:
        status = "🛡️ 保护" if df in protected_paths else "🗑️ 删除"
        print(f"   {status} {df}")
    
    print(f"\n🛡️ 保护文件: {len(protected_files)} 个")
    for pf in protected_files:
        print(f"   🛡️ {pf}")


def run_test():
    """运行测试"""
    print("\n🚀 开始测试智能删除功能")
    print("=" * 50)
    
    # 设置环境变量（测试用）
    os.environ['HUAWEI_ACCESS_KEY'] = 'test_access_key'
    os.environ['HUAWEI_SECRET_KEY'] = 'test_secret_key'
    
    print("📝 运行命令示例：")
    print("./run_smart_delete.sh -c test_config.json -f test_delete_list.txt -p test_protected_files.txt -d")
    print("\n或者直接使用Python：")
    print("python smart_obs_deleter.py --config test_config.json --file-list test_delete_list.txt --protected-files test_protected_files.txt")
    
    print("\n⚠️ 注意：这是试运行模式，不会实际删除文件")
    print("💡 提示：您可以修改配置文件中的 enable_dry_run 为 false 来进行实际删除测试")


def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_config.json',
        'test_delete_list.txt', 
        'test_protected_files.txt',
        'smart_delete_checkpoint.json'
    ]
    
    print("\n🧹 清理测试文件...")
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"   ✅ 已删除: {file}")
    
    # 清理日志目录
    logs_dir = Path('logs')
    if logs_dir.exists():
        for log_file in logs_dir.glob('smart_obs_delete_*.log'):
            log_file.unlink()
            print(f"   ✅ 已删除: {log_file}")


def main():
    """主函数"""
    print("🧪 智能删除功能测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作：")
        print("1. 创建测试文件")
        print("2. 显示测试场景")
        print("3. 运行测试说明")
        print("4. 清理测试文件")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            create_test_files()
        elif choice == '2':
            if os.path.exists('test_delete_list.txt'):
                show_test_scenario()
            else:
                print("❌ 请先创建测试文件")
        elif choice == '3':
            run_test()
        elif choice == '4':
            cleanup_test_files()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")


if __name__ == '__main__':
    main()
