# 智能删除功能使用指南

## 🎯 核心功能

智能删除工具专为您的需求设计，具备以下核心功能：

### ✅ 保护文件功能
- 自动跳过 `fail_decoded.txt` 中列出的文件
- 支持自定义保护文件列表
- 确保重要文件不会被误删

### ✅ 断点续传功能
- 程序中断后可以从上次停止的地方继续
- 自动保存进度检查点
- 支持从任意行号恢复执行

### ✅ 智能进度管理
- 实时进度报告
- 详细的删除统计
- 预估剩余时间

## 🚀 快速开始

### 1. 准备文件

确保您有以下文件：
- `delete_list.txt` - 要删除的文件清单
- `fail_decoded.txt` - 保护文件列表（不删除）
- `obs_config.json` - OBS配置文件

### 2. 试运行（强烈推荐）

```bash
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt -d
```

### 3. 实际删除

确认试运行结果无误后：

```bash
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt
```

## 📊 断点续传示例

### 场景：处理100万个文件

假设您有一个包含100万行的删除清单：

```bash
# 开始删除
./run_smart_delete.sh -f million_files.txt -p fail_decoded.txt

# 程序运行到30万行时意外中断...
# 检查当前进度
python checkpoint_manager.py status

# 输出示例：
# 📍 最后处理行: 300,000
# 📈 总处理文件数: 299,850
# ✅ 成功删除: 280,000
# 🛡️ 保护文件跳过: 15,000
```

### 恢复执行

程序中断后，直接重新运行相同命令即可自动恢复：

```bash
# 自动从第30万行继续
./run_smart_delete.sh -f million_files.txt -p fail_decoded.txt

# 输出：
# 发现检查点: 上次处理到第 300,000 行
# 剩余待处理: 700,000 个文件
```

## 🛡️ 保护文件机制

### 工作原理

1. **加载保护列表**：程序启动时加载 `fail_decoded.txt`
2. **智能识别**：支持完整URL和文件路径两种格式
3. **自动跳过**：遇到保护文件时自动跳过并记录

### 保护文件格式支持

```bash
# 支持完整URL格式
http://vodcosqy.zshtys888.com/188_dir/file1.ts

# 支持直接路径格式  
188_dir/file1.ts
42_dir/file2.ts
```

### 自定义保护文件

```bash
# 使用自定义保护文件列表
./run_smart_delete.sh -f delete_list.txt -p my_protected_files.txt
```

## 📈 进度监控

### 实时查看进度

```bash
# 查看当前状态
python checkpoint_manager.py status

# 估算剩余时间
python checkpoint_manager.py estimate --total-files 1000000
```

### 输出示例

```
📊 检查点状态信息
==================================================
📍 最后处理行: 450,000
📈 总处理文件数: 449,200
✅ 成功删除: 420,000
❌ 删除失败: 1,200
⚠️  文件不存在: 13,000
🛡️  保护文件跳过: 15,000
📊 成功率: 93.5%
🕐 开始时间: 2024-01-15T10:00:00
⏱️  运行时长: 2.5 小时
🚀 处理速度: 179,680 文件/小时

剩余文件估算
==============================
📁 总文件数: 1,000,000
✅ 已处理: 450,000
⏳ 剩余: 550,000
📊 进度: 45.0%
⏰ 预计剩余时间: 3.1 小时
🏁 预计完成时间: 2024-01-15 15:36:00
```

## 🔧 高级功能

### 检查点管理

```bash
# 重置检查点（从头开始）
python checkpoint_manager.py reset

# 手动设置检查点到指定行
python checkpoint_manager.py set 500000

# 备份当前检查点
python checkpoint_manager.py backup
```

### 强制从头开始

```bash
# 忽略检查点，从头开始
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt -n
```

### 重置检查点后开始

```bash
# 重置检查点并开始
./run_smart_delete.sh -f delete_list.txt -p fail_decoded.txt -r
```

## ⚠️ 注意事项

### 1. 保护文件优先级
- 保护文件列表中的文件**绝对不会**被删除
- 即使在删除清单中，也会被自动跳过

### 2. 检查点安全性
- 检查点文件自动备份
- 支持手动备份和恢复
- 程序异常退出时检查点仍然有效

### 3. 性能优化
- 默认每处理10,000个文件报告一次进度
- 每处理50,000个文件保存一次检查点
- 可在配置文件中调整这些参数

### 4. 日志记录
- 所有操作都有详细日志记录
- 日志文件保存在 `logs/` 目录
- 包含每个文件的处理结果

## 🆘 故障排除

### 问题1：程序意外中断
**解决方案**：直接重新运行相同命令，程序会自动从检查点恢复

### 问题2：检查点文件损坏
```bash
# 重置检查点
python checkpoint_manager.py reset
```

### 问题3：需要跳过某些已处理的文件
```bash
# 设置检查点到指定行
python checkpoint_manager.py set 600000
```

### 问题4：查看详细错误信息
```bash
# 查看最新日志
tail -f logs/smart_obs_delete_*.log

# 搜索错误信息
grep "ERROR" logs/smart_obs_delete_*.log
```

## 📝 最佳实践

1. **始终先试运行**：使用 `-d` 参数进行试运行
2. **定期检查进度**：使用检查点管理工具监控进度
3. **备份重要数据**：删除前确保有数据备份
4. **监控日志**：定期查看日志文件了解详细情况
5. **合理设置保护文件**：确保重要文件在保护列表中
